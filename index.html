<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Random Rozlos<PERSON>k</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            height: 100vh;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 100;
            pointer-events: none;
        }

        .controls {
            position: absolute;
            top: 20px;
            left: 20px;
            pointer-events: all;
        }

        .file-input-wrapper {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .file-input-wrapper label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #333;
        }

        input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 2px dashed #667eea;
            border-radius: 5px;
            background: white;
        }

        .draw-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            pointer-events: all;
        }

        .draw-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        .draw-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .winner-display {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            backdrop-filter: blur(15px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            opacity: 0;
            scale: 0;
            transition: all 0.5s ease;
            pointer-events: all;
        }

        .winner-display.show {
            opacity: 1;
            scale: 1;
        }

        .winner-name {
            font-size: 2.5em;
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .winner-subtitle {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }

        .close-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
        }

        .names-list {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            max-width: 200px;
            max-height: 300px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            pointer-events: all;
        }

        .names-list h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .names-list ul {
            list-style: none;
        }

        .names-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
            color: #666;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="ui">
            <div class="controls">
                <div class="file-input-wrapper">
                    <label for="nameFile">Nahrajte soubor se jmény (.txt):</label>
                    <input type="file" id="nameFile" accept=".txt" />
                </div>
                <button id="drawButton" class="draw-button" disabled>🎲 VYLOSOVAT!</button>
            </div>

            <div class="names-list" id="namesList" style="display: none;">
                <h3>Načtená jména:</h3>
                <ul id="namesUl"></ul>
            </div>

            <div class="winner-display" id="winnerDisplay">
                <div class="winner-name" id="winnerName"></div>
                <div class="winner-subtitle">🎉 VÝHERCE! 🎉</div>
                <button class="close-button" id="closeButton">Zavřít</button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
