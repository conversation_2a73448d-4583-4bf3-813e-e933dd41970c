// 3D Random Rozlosovák
class RandomDrawer3D {
    constructor() {
        this.names = [];
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.cubes = [];
        this.isDrawing = false;
        this.font = null;
        this.winnerText = null;

        this.loadFont().then(() => {
            this.init();
            this.setupEventListeners();
            this.animate();
        });
    }

    async loadFont() {
        return new Promise((resolve) => {
            const loader = new THREE.FontLoader();
            // Použijeme z<PERSON>ladní font z Three.js examples
            loader.load('https://threejs.org/examples/fonts/helvetiker_regular.typeface.json', (font) => {
                this.font = font;
                resolve();
            });
        });
    }

    init() {
        // Vytvoření scény
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x667eea, 10, 50);

        // Kamera
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.camera.position.set(0, 5, 15);
        this.camera.lookAt(0, 0, 0);

        // Renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x000000, 0);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.getElementById('container').appendChild(this.renderer.domElement);

        // Osvětlení
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // Bodové světlo pro efekt
        const pointLight = new THREE.PointLight(0xff6b6b, 1, 100);
        pointLight.position.set(0, 10, 0);
        this.scene.add(pointLight);

        // Podlaha
        const floorGeometry = new THREE.PlaneGeometry(50, 50);
        const floorMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x333333, 
            transparent: true, 
            opacity: 0.3 
        });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2;
        floor.position.y = -5;
        floor.receiveShadow = true;
        this.scene.add(floor);

        // Částice na pozadí
        this.createParticles();

        // Resize handler
        window.addEventListener('resize', () => this.onWindowResize());
    }

    createParticles() {
        const particlesGeometry = new THREE.BufferGeometry();
        const particlesCount = 1000;
        const posArray = new Float32Array(particlesCount * 3);

        for (let i = 0; i < particlesCount * 3; i++) {
            posArray[i] = (Math.random() - 0.5) * 100;
        }

        particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
        
        const particlesMaterial = new THREE.PointsMaterial({
            size: 0.1,
            color: 0xffffff,
            transparent: true,
            opacity: 0.6
        });

        const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
        this.scene.add(particlesMesh);
        this.particles = particlesMesh;
    }

    createNameCubes() {
        // Vyčistit existující kostky
        this.cubes.forEach(cube => this.scene.remove(cube));
        this.cubes = [];

        if (this.names.length === 0) return;

        const colors = [0xff6b6b, 0x4ecdc4, 0x45b7d1, 0xf9ca24, 0xf0932b, 0xeb4d4b, 0x6c5ce7];
        
        this.names.forEach((name, index) => {
            const geometry = new THREE.BoxGeometry(2, 2, 2);
            const material = new THREE.MeshPhongMaterial({ 
                color: colors[index % colors.length],
                shininess: 100
            });
            
            const cube = new THREE.Mesh(geometry, material);
            cube.castShadow = true;
            cube.receiveShadow = true;
            
            // Rozmístění kostek do kruhu
            const angle = (index / this.names.length) * Math.PI * 2;
            const radius = Math.max(5, this.names.length * 0.8);
            cube.position.x = Math.cos(angle) * radius;
            cube.position.z = Math.sin(angle) * radius;
            cube.position.y = 0;
            
            cube.userData = { name: name, originalPosition: cube.position.clone() };
            
            this.scene.add(cube);
            this.cubes.push(cube);
        });
    }

    async startDrawAnimation() {
        if (this.isDrawing || this.names.length === 0) return;
        
        this.isDrawing = true;
        document.getElementById('drawButton').disabled = true;

        // Animace rotace všech kostek
        const rotationDuration = 3000; // 3 sekundy
        const startTime = Date.now();

        return new Promise((resolve) => {
            const rotateAnimation = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / rotationDuration, 1);
                
                this.cubes.forEach((cube, index) => {
                    // Rychlá rotace
                    cube.rotation.x += 0.1 * (1 - progress * 0.8);
                    cube.rotation.y += 0.15 * (1 - progress * 0.8);
                    cube.rotation.z += 0.08 * (1 - progress * 0.8);
                    
                    // Vertikální pohyb
                    cube.position.y = Math.sin(elapsed * 0.01 + index) * 2;
                    
                    // Změna velikosti
                    const scale = 1 + Math.sin(elapsed * 0.02 + index) * 0.3;
                    cube.scale.set(scale, scale, scale);
                });

                if (progress < 1) {
                    requestAnimationFrame(rotateAnimation);
                } else {
                    this.selectWinner().then(resolve);
                }
            };
            
            rotateAnimation();
        });
    }

    async selectWinner() {
        // Výběr náhodného vítěze
        const winnerIndex = Math.floor(Math.random() * this.names.length);
        const winnerCube = this.cubes[winnerIndex];
        const winnerName = winnerCube.userData.name;

        // Animace ostatních kostek - zmizení
        const fadePromises = this.cubes.map((cube, index) => {
            if (index === winnerIndex) return Promise.resolve();
            
            return new Promise((resolve) => {
                const fadeAnimation = () => {
                    cube.material.opacity -= 0.05;
                    cube.position.y -= 0.2;
                    cube.scale.multiplyScalar(0.95);
                    
                    if (cube.material.opacity <= 0) {
                        this.scene.remove(cube);
                        resolve();
                    } else {
                        requestAnimationFrame(fadeAnimation);
                    }
                };
                cube.material.transparent = true;
                fadeAnimation();
            });
        });

        // Animace vítězné kostky
        const winnerAnimation = new Promise((resolve) => {
            let bounceCount = 0;
            const maxBounces = 5;
            
            const bounce = () => {
                winnerCube.position.y = Math.abs(Math.sin(bounceCount * 0.5)) * 5;
                winnerCube.rotation.y += 0.2;
                
                const scale = 1.5 + Math.sin(bounceCount * 0.3) * 0.5;
                winnerCube.scale.set(scale, scale, scale);
                
                bounceCount += 0.2;
                
                if (bounceCount < maxBounces * Math.PI) {
                    requestAnimationFrame(bounce);
                } else {
                    resolve();
                }
            };
            
            bounce();
        });

        await Promise.all([...fadePromises, winnerAnimation]);
        
        // Zobrazit výsledek jako 3D text
        await this.showWinner3D(winnerName);

        this.isDrawing = false;
        document.getElementById('drawButton').disabled = false;
        document.getElementById('resetButton').style.display = 'block';
    }

    async showWinner3D(name) {
        // Vytvořit 3D text
        const textGeometry = new THREE.TextGeometry(name.toUpperCase(), {
            font: this.font,
            size: 2,
            height: 0.5,
            curveSegments: 12,
            bevelEnabled: true,
            bevelThickness: 0.1,
            bevelSize: 0.05,
            bevelOffset: 0,
            bevelSegments: 5
        });

        // Vycentrovat text
        textGeometry.computeBoundingBox();
        const centerOffsetX = -0.5 * (textGeometry.boundingBox.max.x - textGeometry.boundingBox.min.x);
        const centerOffsetY = -0.5 * (textGeometry.boundingBox.max.y - textGeometry.boundingBox.min.y);

        // Materiál s gradientem efektem
        const textMaterial = new THREE.MeshPhongMaterial({
            color: 0xff6b6b,
            shininess: 100,
            specular: 0xffffff
        });

        this.winnerText = new THREE.Mesh(textGeometry, textMaterial);
        this.winnerText.position.set(centerOffsetX, centerOffsetY + 8, 0);
        this.winnerText.castShadow = true;
        this.winnerText.receiveShadow = true;

        // Začít s neviditelným textem
        this.winnerText.scale.set(0, 0, 0);
        this.scene.add(this.winnerText);

        // Animace objevení textu
        return new Promise((resolve) => {
            let animationProgress = 0;
            const animationSpeed = 0.05;

            const textAnimation = () => {
                animationProgress += animationSpeed;

                // Postupné zvětšování
                const scale = Math.min(animationProgress, 1);
                const bounceScale = scale + Math.sin(animationProgress * 10) * 0.1 * (1 - scale);
                this.winnerText.scale.set(bounceScale, bounceScale, bounceScale);

                // Rotace
                this.winnerText.rotation.y = Math.sin(animationProgress * 2) * 0.2;

                // Vertikální pohyb
                this.winnerText.position.y = centerOffsetY + 8 + Math.sin(animationProgress * 5) * 0.5;

                // Změna barvy
                const hue = (animationProgress * 360) % 360;
                this.winnerText.material.color.setHSL(hue / 360, 0.8, 0.6);

                if (animationProgress < 2) {
                    requestAnimationFrame(textAnimation);
                } else {
                    // Konfety efekt
                    this.createConfetti();
                    resolve();
                }
            };

            textAnimation();
        });
    }

    createConfetti() {
        const confettiCount = 200;
        const confetti = [];
        
        for (let i = 0; i < confettiCount; i++) {
            const geometry = new THREE.PlaneGeometry(0.2, 0.2);
            const material = new THREE.MeshBasicMaterial({
                color: Math.random() * 0xffffff,
                side: THREE.DoubleSide
            });
            
            const piece = new THREE.Mesh(geometry, material);
            piece.position.set(
                (Math.random() - 0.5) * 20,
                10 + Math.random() * 5,
                (Math.random() - 0.5) * 20
            );
            
            piece.velocity = {
                x: (Math.random() - 0.5) * 0.2,
                y: -Math.random() * 0.1 - 0.05,
                z: (Math.random() - 0.5) * 0.2
            };
            
            this.scene.add(piece);
            confetti.push(piece);
        }
        
        // Animace konfet
        const animateConfetti = () => {
            confetti.forEach((piece, index) => {
                piece.position.add(new THREE.Vector3(piece.velocity.x, piece.velocity.y, piece.velocity.z));
                piece.rotation.x += 0.1;
                piece.rotation.y += 0.1;
                
                if (piece.position.y < -10) {
                    this.scene.remove(piece);
                    confetti.splice(index, 1);
                }
            });
            
            if (confetti.length > 0) {
                requestAnimationFrame(animateConfetti);
            }
        };
        
        animateConfetti();
    }

    setupEventListeners() {
        // Nahrání souboru
        document.getElementById('nameFile').addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const content = e.target.result;
                    this.names = content.split('\n')
                        .map(name => name.trim())
                        .filter(name => name.length > 0);
                    
                    this.updateNamesList();
                    this.createNameCubes();
                    document.getElementById('drawButton').disabled = false;
                };
                reader.readAsText(file);
            }
        });

        // Tlačítko losování
        document.getElementById('drawButton').addEventListener('click', () => {
            this.startDrawAnimation();
        });

        // Tlačítko reset
        document.getElementById('resetButton').addEventListener('click', () => {
            this.resetScene();
        });
    }

    resetScene() {
        // Odstranit 3D text pokud existuje
        if (this.winnerText) {
            this.scene.remove(this.winnerText);
            this.winnerText = null;
        }

        // Obnovit kostky
        this.createNameCubes();

        // Skrýt reset tlačítko
        document.getElementById('resetButton').style.display = 'none';
    }

    updateNamesList() {
        const namesList = document.getElementById('namesList');
        const namesUl = document.getElementById('namesUl');
        
        namesUl.innerHTML = '';
        this.names.forEach(name => {
            const li = document.createElement('li');
            li.textContent = name;
            namesUl.appendChild(li);
        });
        
        namesList.style.display = this.names.length > 0 ? 'block' : 'none';
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        
        // Rotace částic
        if (this.particles) {
            this.particles.rotation.y += 0.001;
        }
        
        // Jemná animace kostek když nejsou v losování
        if (!this.isDrawing) {
            this.cubes.forEach((cube, index) => {
                cube.position.y = Math.sin(Date.now() * 0.001 + index) * 0.2;
                cube.rotation.y += 0.005;
            });
        }

        // Animace 3D textu pokud existuje
        if (this.winnerText) {
            this.winnerText.rotation.y += 0.01;
            this.winnerText.position.y += Math.sin(Date.now() * 0.003) * 0.05;

            // Duhový efekt
            const time = Date.now() * 0.001;
            const hue = (time * 50) % 360;
            this.winnerText.material.color.setHSL(hue / 360, 0.8, 0.6);
        }
        
        this.renderer.render(this.scene, this.camera);
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
}

// Spuštění aplikace
document.addEventListener('DOMContentLoaded', () => {
    new RandomDrawer3D();
});
